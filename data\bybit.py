# trading_map/data/fetch_bybit.py
import asyncio
import websockets
import json

class BybitWebSocket:
    def __init__(self, symbol="BTCUSDT", depth_level=25):
        self.ws_url = "wss://stream.bybit.com/v5/public/linear"
        self.symbol = symbol.upper()
        self.depth_level = depth_level
        self.orderbook = {}
        self.trades = []

    async def connect(self):
        async with websockets.connect(self.ws_url) as ws:
            await self.subscribe(ws)
            async for message in ws:
                await self.handle_message(message)

    async def subscribe(self, ws):
        sub_msg = {
            "op": "subscribe",
            "args": [
                f"orderbook.{self.depth_level}.{self.symbol}",
                f"publicTrade.{self.symbol}"
            ]
        }
        await ws.send(json.dumps(sub_msg))

    async def handle_message(self, message):
        msg = json.loads(message)
        topic = msg.get("topic", "")

        # 오더북
        if topic.startswith("orderbook"):
            data = msg.get("data", {})
            if "a" in data and "b" in data:
                self.orderbook = {
                    "asks": data["a"],  # [price, size]
                    "bids": data["b"]
                }

        # 체결
        elif topic.startswith("publicTrade"):
            trades = msg.get("data", [])
            for trade in trades:
                self.trades.append({
                    "timestamp": trade["T"],
                    "price": float(trade["p"]),
                    "size": float(trade["v"]),
                    "side": trade["S"],  # Buy/Sell
                })
                if len(self.trades) > 500:
                    self.trades.pop(0)

    def get_orderbook(self):
        return self.orderbook

    def get_trades(self):
        return self.trades

# 모듈 단독 테스트
if __name__ == "__main__":
    bybit_ws = BybitWebSocket()

    async def run_ws():
        await bybit_ws.connect()

    asyncio.run(run_ws())
