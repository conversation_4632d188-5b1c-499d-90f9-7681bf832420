import streamlit as st
import asyncio
import threading
import signal
import sys
from queue import Queue
from utils.ws import WSManager
from modules.viz import draw_chart_with_bubble
from modules.sig import SignalGenerator
from modules.order import OrderManager
from dotenv import load_dotenv
import os
import logging

# 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# ===============================
# 🔐 비밀번호 인증 시스템
# ===============================
st.set_page_config(page_title="TradingMap", layout="wide")
st.title("🌍 실시간 거래 시각화 - Bookmap 스타일")

# 환경변수에서 비밀번호 로드 (보안 개선)
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "hope4africa")
password = st.text_input("🔐 관리자 비밀번호 입력", type="password")

if password != ADMIN_PASSWORD:
    st.warning("🚫 인증 실패: 올바른 비밀번호를 입력하세요.")
    st.stop()

st.success("✅ 인증 성공: 관리자 권한 획득")

# ===============================
# 📈 실시간 차트 + 시그널 출력
# ===============================

symbol = st.selectbox("📊 종목 선택", ["BTCUSDT", "ETHUSDT"])
placeholder = st.empty()
st.markdown("### 🛡️ 실시간 자동매매 시그널")
signal_box = st.empty()

# 상태 공유
data_queue = Queue()  # 스레드 안전 큐
signal_generator = SignalGenerator()
order_manager = OrderManager()

# 웹소켓 관리자 초기화 (스레드 안전 종료 플래그 추가)
if 'ws_manager' not in st.session_state:
    st.session_state.ws_manager = None
    st.session_state.ws_thread = None
    st.session_state.ui_thread = None
    st.session_state.stop_flag = threading.Event()  # 스레드 안전 종료 플래그

# 종목 변경 시 웹소켓 재연결
if st.session_state.get('current_symbol') != symbol:
    st.session_state['current_symbol'] = symbol

    # 기존 스레드들 안전하게 종료
    st.session_state.stop_flag.set()  # 종료 신호 전송

    # 기존 연결 종료 (리소스 누수 방지)
    if st.session_state.ws_manager:
        try:
            # 기존 이벤트 루프가 있는지 확인
            try:
                loop = asyncio.get_running_loop()
                # 이미 실행 중인 루프가 있으면 태스크로 실행
                asyncio.create_task(st.session_state.ws_manager.close())
            except RuntimeError:
                # 실행 중인 루프가 없으면 새로 생성
                asyncio.run(st.session_state.ws_manager.close())
        except Exception as e:
            logger.error(f"웹소켓 종료 중 오류: {str(e)}")
        finally:
            st.session_state.ws_manager = None

    # 새 웹소켓 연결
    st.session_state.ws_manager = WSManager(symbol=symbol)
    st.session_state.stop_flag.clear()  # 종료 플래그 리셋
    
    # 실시간 수신 스레드 시작 (개선된 예외 처리 및 종료 조건)
    def ws_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        async def runner():
            try:
                await st.session_state.ws_manager.connect()
                while not st.session_state.stop_flag.is_set():
                    try:
                        # 타임아웃을 설정하여 무한 대기 방지
                        data = await asyncio.wait_for(
                            st.session_state.ws_manager.get_data(),
                            timeout=5.0
                        )
                        if data:
                            data_queue.put(data)
                    except asyncio.TimeoutError:
                        # 타임아웃은 정상적인 상황으로 처리
                        continue
                    except Exception as e:
                        logger.error(f"데이터 수신 중 오류: {str(e)}")
                        break
            except Exception as e:
                logger.error(f"웹소켓 연결 오류: {str(e)}")
            finally:
                logger.info("웹소켓 스레드 종료")

        try:
            loop.run_until_complete(runner())
        except Exception as e:
            logger.error(f"웹소켓 스레드 실행 오류: {str(e)}")
        finally:
            loop.close()
    
    # 기존 스레드 안전하게 종료
    if st.session_state.ws_thread and st.session_state.ws_thread.is_alive():
        logger.info("기존 웹소켓 스레드 종료 대기 중...")
        st.session_state.ws_thread.join(timeout=3)  # 타임아웃 증가
        if st.session_state.ws_thread.is_alive():
            logger.warning("웹소켓 스레드가 정상 종료되지 않았습니다.")

    st.session_state.ws_thread = threading.Thread(target=ws_thread, daemon=True)
    st.session_state.ws_thread.start()
    logger.info("새 웹소켓 스레드 시작")

    # UI 업데이트 함수 (예외 처리 강화)
    def ui_update_loop():
        logger.info("UI 업데이트 스레드 시작")

        while not st.session_state.stop_flag.is_set():
            try:
                if not data_queue.empty():
                    data = data_queue.get()

                    # 데이터 유효성 검사
                    if not isinstance(data, dict):
                        logger.warning(f"잘못된 데이터 형식: {type(data)}")
                        continue

                    # 차트 데이터 추출 (안전한 접근)
                    trades = data.get('trades', [])
                    orderbook = data.get('orderbook', {})
                    cvd = data.get('cvd', [])

                    # 차트 그리기 (예외 처리 추가)
                    try:
                        fig = draw_chart_with_bubble(trades, orderbook, cvd)
                        placeholder.plotly_chart(fig, use_container_width=True)
                    except Exception as e:
                        logger.error(f"차트 렌더링 오류: {str(e)}")
                        signal_box.error(f"📊 차트 렌더링 실패: {str(e)}")
                        continue

                    # 매매 신호 처리 (예외 처리 추가)
                    try:
                        signal, price, marker = signal_generator.detect_signals(trades)

                        if signal == "buy" and order_manager.should_buy(price, marker):
                            order = order_manager.execute_buy(price)
                            signal_box.success(f"🟢 매수 신호 (Step {order['step']}): {symbol} @ {order['price']:.2f}")

                        elif signal == "sell" and order_manager.has_position():
                            order = order_manager.execute_sell(price)
                            signal_box.error(f"🔴 매도 신호: {symbol} @ {price:.2f}")
                            order_manager.reset_position()
                    except Exception as e:
                        logger.error(f"신호 처리 오류: {str(e)}")
                        signal_box.warning(f"⚠️ 신호 처리 실패: {str(e)}")

            except Exception as e:
                logger.error(f"UI 업데이트 중 예상치 못한 오류: {str(e)}")

            # CPU 사용량 조절 (종료 플래그 확인과 함께)
            st.session_state.stop_flag.wait(0.1)

        logger.info("UI 업데이트 스레드 종료")
    
    # 기존 UI 스레드 안전하게 종료
    if st.session_state.ui_thread and st.session_state.ui_thread.is_alive():
        logger.info("기존 UI 스레드 종료 대기 중...")
        st.session_state.ui_thread.join(timeout=3)  # 타임아웃 증가
        if st.session_state.ui_thread.is_alive():
            logger.warning("UI 스레드가 정상 종료되지 않았습니다.")

    st.session_state.ui_thread = threading.Thread(target=ui_update_loop, daemon=True)
    st.session_state.ui_thread.start()
    logger.info("새 UI 스레드 시작")

# 초기 상태 메시지 (예외 처리 추가)
try:
    if data_queue.empty():
        signal_box.info("📡 데이터 수신 대기 중...")
except Exception as e:
    logger.error(f"초기 상태 메시지 표시 오류: {str(e)}")
    signal_box.warning("⚠️ 시스템 초기화 중...")

# 애플리케이션 종료 시 리소스 정리
def cleanup_resources():
    """애플리케이션 종료 시 리소스 정리"""
    try:
        if 'stop_flag' in st.session_state:
            st.session_state.stop_flag.set()
        logger.info("리소스 정리 완료")
    except Exception as e:
        logger.error(f"리소스 정리 중 오류: {str(e)}")

# Streamlit 종료 시 정리 함수 등록
import atexit
atexit.register(cleanup_resources)