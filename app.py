import streamlit as st
import asyncio
import threading
from queue import Queue
from utils.ws import WSManager
from modules.viz import draw_chart_with_bubble
from modules.sig import SignalGenerator
from modules.order import OrderManager
from dotenv import load_dotenv
import os

load_dotenv()

# ===============================
# 🔐 비밀번호 인증 시스템
# ===============================
st.set_page_config(page_title="TradingMap", layout="wide")
st.title("🌍 실시간 거래 시각화 - Bookmap 스타일")

ADMIN_PASSWORD = "hope4africa"
password = st.text_input("🔐 관리자 비밀번호 입력", type="password")

if password != ADMIN_PASSWORD:
    st.warning("🚫 인증 실패: 올바른 비밀번호를 입력하세요.")
    st.stop()

st.success("✅ 인증 성공: 관리자 권한 획득")

# ===============================
# 📈 실시간 차트 + 시그널 출력
# ===============================

symbol = st.selectbox("📊 종목 선택", ["BTCUSDT", "ETHUSDT"])
placeholder = st.empty()
st.markdown("### 🛡️ 실시간 자동매매 시그널")
signal_box = st.empty()

# 상태 공유
data_queue = Queue()  # 스레드 안전 큐
signal_generator = SignalGenerator()
order_manager = OrderManager()

# 웹소켓 관리자 초기화
if 'ws_manager' not in st.session_state:
    st.session_state.ws_manager = None
    st.session_state.ws_thread = None
    st.session_state.ui_thread = None

# 종목 변경 시 웹소켓 재연결
if st.session_state.get('current_symbol') != symbol:
    st.session_state['current_symbol'] = symbol
    
    # 기존 연결 종료
    if st.session_state.ws_manager:
        asyncio.run(st.session_state.ws_manager.close())
        st.session_state.ws_manager = None
    
    # 새 웹소켓 연결
    st.session_state.ws_manager = WSManager(symbol=symbol)
    
    # 실시간 수신 스레드 시작
    def ws_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def runner():
            try:
                await st.session_state.ws_manager.connect()
                while True:
                    data = await st.session_state.ws_manager.get_data()
                    if data:
                        data_queue.put(data)
            except Exception as e:
                st.error(f"웹소켓 오류: {str(e)}")
        
        loop.run_until_complete(runner())
    
    # 기존 스레드 종료
    if st.session_state.ws_thread and st.session_state.ws_thread.is_alive():
        st.session_state.ws_thread.join(timeout=1)
    
    st.session_state.ws_thread = threading.Thread(target=ws_thread, daemon=True)
    st.session_state.ws_thread.start()

    # UI 업데이트 함수
    def ui_update_loop():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        while True:
            if not data_queue.empty():
                data = data_queue.get()
                
                # 차트 데이터 추출 (가정: data 딕셔너리 구조)
                trades = data.get('trades', [])
                orderbook = data.get('orderbook', {})
                cvd = data.get('cvd', [])
                
                # 차트 그리기
                fig = draw_chart_with_bubble(trades, orderbook, cvd)
                placeholder.plotly_chart(fig, use_container_width=True)
                
                # 매매 신호 처리
                signal, price, marker = signal_generator.detect_signals(trades)
                
                if signal == "buy" and order_manager.should_buy(price, marker):
                    order = order_manager.execute_buy(price)
                    signal_box.success(f"🟢 매수 신호 (Step {order['step']}): {symbol} @ {order['price']:.2f}")
                
                elif signal == "sell" and order_manager.has_position():
                    order = order_manager.execute_sell(price)
                    signal_box.error(f"🔴 매도 신호: {symbol} @ {price:.2f}")
                    order_manager.reset_position()
            
            # CPU 사용량 조절
            threading.Event().wait(0.1)
    
    # 기존 UI 스레드 종료
    if st.session_state.ui_thread and st.session_state.ui_thread.is_alive():
        st.session_state.ui_thread.join(timeout=1)
    
    st.session_state.ui_thread = threading.Thread(target=ui_update_loop, daemon=True)
    st.session_state.ui_thread.start()

# 초기 상태 메시지
if data_queue.empty():
    signal_box.info("📡 데이터 수신 대기 중...")