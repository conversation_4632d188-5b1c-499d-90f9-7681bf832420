# trading_map/utils/ws.py
import asyncio
from data.bybit import BybitWebSocket

class WSManager:
    def __init__(self, symbol="BTCUSDT"):
        self.symbol = symbol
        self.bybit = BybitWebSocket(symbol=self.symbol)
        self.queue = asyncio.Queue()

    async def start(self):
        while True:
            try:
                print("[WS 연결 시도 중...]")
                await self.bybit.connect()
                print("[WS 연결 성공]")
                await self._stream_data()
            except Exception as e:
                print(f"[WS 연결 실패 또는 중단] 재시도 중: {e}")
                await asyncio.sleep(3)

    async def _stream_data(self):
        while True:
            try:
                # 실제 수신 타임아웃 적용 (예: 10초 이내 응답 없으면 예외 발생)
                async with asyncio.timeout(10):
                    trades = self.bybit.get_trades()
                    orderbook = self.bybit.get_orderbook()
                    await self.queue.put({
                        "trades": trades,
                        "orderbook": orderbook
                    })
                    await asyncio.sleep(0.3)
            except asyncio.TimeoutError:
                print("[WS 타임아웃] 응답 지연 - 연결 재시도 예정")
                raise  # start()의 try에서 재연결됨
            except Exception as e:
                print(f"[WS 데이터 수신 중 오류]: {e}")
                raise

    async def push_data(self):
        while True:
            await asyncio.sleep(0.1)  # 최소 대기 시간 유지 (실시간 신호 충돌 방지)

    async def get_data(self):
        return await self.queue.get()
