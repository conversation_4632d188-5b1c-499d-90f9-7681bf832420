import websockets
import json
from collections import deque
import asyncio
import time

class BybitWebSocket:
    def __init__(self, symbol="BTCUSDT", depth_level=25, update_callback=None):
        self.symbol = symbol
        self.depth_level = depth_level
        self.update_callback = update_callback
        self.trades = deque(maxlen=200)
        self.orderbook = {'bids': [], 'asks': []}
        self.ws = None
        self.running = False
        self.reconnect_delay = 1  # Initial reconnect delay in seconds

    async def connect(self):
        self.running = True
        url = "wss://stream.bybit.com/v5/public/linear"
        
        while self.running:
            try:
                async with websockets.connect(url) as self.ws:
                    self.reconnect_delay = 1  # Reset delay after successful connection
                    
                    # Subscribe to trade and orderbook streams
                    subscribe_msg = {
                        "op": "subscribe",
                        "args": [
                            f"publicTrade.{self.symbol}",
                            f"orderbook.{self.symbol}.{self.depth_level}"
                        ]
                    }
                    await self.ws.send(json.dumps(subscribe_msg))
                    
                    # Continuously receive messages
                    async for message in self.ws:
                        self.handle_message(json.loads(message))
            
            except (websockets.ConnectionClosed, ConnectionRefusedError) as e:
                print(f"Connection error: {e}. Reconnecting in {self.reconnect_delay}s")
                await asyncio.sleep(self.reconnect_delay)
                self.reconnect_delay = min(self.reconnect_delay * 2, 60)  # Exponential backoff
            except Exception as e:
                print(f"Unexpected error: {e}")
                self.running = False
                raise

    def handle_message(self, message: dict):
        topic = message.get("topic", "")
        data = message.get("data", [])
        updated = False

        if "publicTrade" in topic:
            for trade in data:
                self.trades.append({
                    "price": float(trade["price"]),
                    "size": float(trade["size"]),
                    "side": trade["side"],
                    "timestamp": trade["ts"]
                })
            updated = True

        elif "orderbook" in topic:
            msg_type = message.get("type")
            if msg_type == "snapshot":
                self.orderbook['bids'] = sorted(
                    [[float(p), float(q)] for p, q in data.get('b', [])],
                    key=lambda x: x[0], reverse=True
                )[:self.depth_level]
                
                self.orderbook['asks'] = sorted(
                    [[float(p), float(q)] for p, q in data.get('a', [])],
                    key=lambda x: x[0]
                )[:self.depth_level]
                
            elif msg_type == "delta":
                self._update_orderbook(data)
                
            updated = True

        if updated and self.update_callback:
            self.update_callback({
                "trades": list(self.trades),
                "orderbook": self.orderbook
            })

    def _update_orderbook(self, data: dict):
        """Process orderbook delta updates"""
        # Helper function to process updates for a single side
        def process_side(current_side, updates, reverse_sort):
            # Convert to dictionary for efficient updates
            side_dict = {price: qty for price, qty in current_side}
            
            # Apply updates
            for price, qty in updates:
                if qty == 0:
                    side_dict.pop(price, None)
                else:
                    side_dict[price] = qty
            
            # Convert back to sorted list
            updated_side = sorted(
                [[price, qty] for price, qty in side_dict.items()],
                key=lambda x: x[0], 
                reverse=reverse_sort
            )
            return updated_side[:self.depth_level]

        # Process bids (sort descending)
        bid_updates = [(float(p), float(q)) for p, q in data.get('b', [])]
        self.orderbook['bids'] = process_side(
            self.orderbook['bids'], bid_updates, True
        )

        # Process asks (sort ascending)
        ask_updates = [(float(p), float(q)) for p, q in data.get('a', [])]
        self.orderbook['asks'] = process_side(
            self.orderbook['asks'], ask_updates, False
        )

    def get_trades(self):
        return list(self.trades)

    def get_orderbook(self):
        return self.orderbook

    async def disconnect(self):
        self.running = False
        if self.ws:
            await self.ws.close()