# modules/viz.py
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np

def draw_chart_with_bubble(trades, orderbook, cvd=None):
    """
    Bookmap 스타일의 실시간 거래 차트를 그립니다.
    
    Args:
        trades: 거래 데이터 리스트
        orderbook: 오더북 데이터 딕셔너리
        cvd: CVD(Cumulative Volume Delta) 데이터 (선택사항)
    
    Returns:
        plotly.graph_objects.Figure: 차트 객체
    """
    try:
        # 서브플롯 생성 (메인 차트 + CVD)
        fig = make_subplots(
            rows=2, cols=1,
            row_heights=[0.8, 0.2],
            subplot_titles=('Price & Volume', 'CVD'),
            vertical_spacing=0.05,
            shared_xaxes=True
        )
        
        # 1. 거래 데이터 처리
        if trades and len(trades) > 0:
            df_trades = pd.DataFrame(trades)
            
            # 가격별 거래량 집계
            buy_trades = df_trades[df_trades['side'] == 'Buy']
            sell_trades = df_trades[df_trades['side'] == 'Sell']
            
            # 매수 거래 (녹색 버블)
            if not buy_trades.empty:
                buy_agg = buy_trades.groupby('price')['size'].sum().reset_index()
                fig.add_trace(
                    go.Scatter(
                        x=buy_agg.index,
                        y=buy_agg['price'],
                        mode='markers',
                        marker=dict(
                            size=buy_agg['size'] * 10,  # 버블 크기
                            color='green',
                            opacity=0.6,
                            sizemode='diameter',
                            sizemin=5,
                            sizemax=50
                        ),
                        name='Buy Volume',
                        hovertemplate='Price: %{y}<br>Volume: %{marker.size}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
            # 매도 거래 (빨간색 버블)
            if not sell_trades.empty:
                sell_agg = sell_trades.groupby('price')['size'].sum().reset_index()
                fig.add_trace(
                    go.Scatter(
                        x=sell_agg.index,
                        y=sell_agg['price'],
                        mode='markers',
                        marker=dict(
                            size=sell_agg['size'] * 10,  # 버블 크기
                            color='red',
                            opacity=0.6,
                            sizemode='diameter',
                            sizemin=5,
                            sizemax=50
                        ),
                        name='Sell Volume',
                        hovertemplate='Price: %{y}<br>Volume: %{marker.size}<extra></extra>'
                    ),
                    row=1, col=1
                )
        
        # 2. 오더북 데이터 처리
        if orderbook and 'bids' in orderbook and 'asks' in orderbook:
            bids = orderbook['bids'][:10]  # 상위 10개
            asks = orderbook['asks'][:10]  # 상위 10개
            
            if bids:
                bid_prices = [float(bid[0]) for bid in bids]
                bid_sizes = [float(bid[1]) for bid in bids]
                
                fig.add_trace(
                    go.Bar(
                        x=bid_sizes,
                        y=bid_prices,
                        orientation='h',
                        marker_color='rgba(0, 255, 0, 0.3)',
                        name='Bids',
                        showlegend=True
                    ),
                    row=1, col=1
                )
            
            if asks:
                ask_prices = [float(ask[0]) for ask in asks]
                ask_sizes = [float(ask[1]) for ask in asks]
                
                fig.add_trace(
                    go.Bar(
                        x=[-size for size in ask_sizes],  # 음수로 표시
                        y=ask_prices,
                        orientation='h',
                        marker_color='rgba(255, 0, 0, 0.3)',
                        name='Asks',
                        showlegend=True
                    ),
                    row=1, col=1
                )
        
        # 3. CVD 차트 (하단)
        if cvd and len(cvd) > 0:
            cvd_df = pd.DataFrame(cvd)
            fig.add_trace(
                go.Scatter(
                    x=cvd_df.index,
                    y=cvd_df.get('value', [0] * len(cvd_df)),
                    mode='lines',
                    line=dict(color='blue', width=2),
                    name='CVD',
                    fill='tonexty'
                ),
                row=2, col=1
            )
        else:
            # CVD 데이터가 없으면 더미 데이터
            fig.add_trace(
                go.Scatter(
                    x=[0, 1],
                    y=[0, 0],
                    mode='lines',
                    line=dict(color='blue', width=2),
                    name='CVD (No Data)'
                ),
                row=2, col=1
            )
        
        # 레이아웃 설정
        fig.update_layout(
            title="Real-time Trading Map (Bookmap Style)",
            height=800,
            showlegend=True,
            template='plotly_dark',
            hovermode='closest'
        )
        
        # X축 설정
        fig.update_xaxes(title_text="Time/Index", row=2, col=1)
        
        # Y축 설정
        fig.update_yaxes(title_text="Price", row=1, col=1)
        fig.update_yaxes(title_text="CVD", row=2, col=1)
        
        return fig
        
    except Exception as e:
        # 오류 발생 시 기본 차트 반환
        fig = go.Figure()
        fig.add_annotation(
            text=f"Chart Error: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="red")
        )
        fig.update_layout(
            title="Chart Error",
            height=400,
            template='plotly_dark'
        )
        return fig

def create_simple_chart():
    """간단한 테스트 차트 생성"""
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=[1, 2, 3, 4],
        y=[10, 11, 12, 13],
        mode='lines+markers',
        name='Test Data'
    ))
    fig.update_layout(
        title="Test Chart",
        height=400,
        template='plotly_dark'
    )
    return fig
